{"name": "portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"dotenv": "^16.4.5", "flowbite-react": "^0.10.2", "lucide-react": "^0.453.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.23.1", "react-vertical-timeline-component": "^3.6.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vite": "^5.2.0"}}