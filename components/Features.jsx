import Image from 'next/image';

const features = [
  {
    name: 'Advanced Analytics',
    description:
      'Get detailed insights into your business performance with our powerful analytics dashboard.',
    icon: '/window.svg',
  },
  {
    name: 'Workflow Automation',
    description:
      'Automate repetitive tasks and streamline your workflow to save time and reduce errors.',
    icon: '/file.svg',
  },
  {
    name: 'Team Collaboration',
    description:
      'Work together seamlessly with your team members, no matter where they are located.',
    icon: '/globe.svg',
  },
  {
    name: 'Secure Data Storage',
    description:
      'Keep your data safe and secure with our enterprise-grade security features.',
    icon: '/file.svg',
  },
  {
    name: 'Mobile Access',
    description:
      'Access your work from anywhere, anytime with our mobile-friendly platform.',
    icon: '/window.svg',
  },
  {
    name: 'Integration Ecosystem',
    description:
      'Connect with your favorite tools and services through our extensive integration options.',
    icon: '/globe.svg',
  },
];

export function Features() {
  return (
    <div className="py-24 sm:py-32 bg-muted/50">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base font-semibold leading-7 text-primary">Everything you need</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Powerful features for your business
          </p>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Our platform provides all the tools you need to manage and grow your business efficiently.
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            {features.map((feature) => (
              <div key={feature.name} className="flex flex-col">
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <div className="h-10 w-10 flex items-center justify-center rounded-lg bg-primary/10">
                    <Image
                      src={feature.icon}
                      alt={feature.name}
                      className="h-6 w-6 text-primary"
                      width={24}
                      height={24}
                    />
                  </div>
                  {feature.name}
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p className="flex-auto">{feature.description}</p>
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </div>
  );
}