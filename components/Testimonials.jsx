import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const testimonials = [
  {
    body: 'SaaSify has completely transformed how our team works. The automation features alone have saved us countless hours every week.',
    author: {
      name: '<PERSON>',
      role: 'CTO at TechCorp',
      image: null,
      initials: '<PERSON><PERSON>',
    },
  },
  {
    body: 'The analytics dashboard gives us insights we never had before. We can now make data-driven decisions with confidence.',
    author: {
      name: '<PERSON>',
      role: 'Product Manager at InnovateCo',
      image: null,
      initials: 'MC',
    },
  },
  {
    body: 'Customer support is exceptional. Any time we have a question, the team responds quickly and effectively.',
    author: {
      name: '<PERSON>',
      role: 'Operations Director at GrowthInc',
      image: null,
      initials: 'ER',
    },
  },
  {
    body: 'We evaluated several platforms before choosing SaaSify. The ease of use and comprehensive feature set made it an easy decision.',
    author: {
      name: '<PERSON>',
      role: 'CEO at StartupHub',
      image: null,
      initials: '<PERSON><PERSON>',
    },
  },
  {
    body: 'The collaboration tools have made remote work seamless for our global team. We can stay connected and productive from anywhere.',
    author: {
      name: '<PERSON>',
      role: 'HR Manager at GlobalTeams',
      image: null,
      initials: 'LP',
    },
  },
  {
    body: 'Implementation was smooth and the ROI was almost immediate. SaaSify has paid for itself many times over.',
    author: {
      name: '<PERSON> <PERSON>',
      role: 'Finance Director at ScaleUp',
      image: null,
      initials: 'JW',
    },
  },
];

export function Testimonials() {
  return (
    <div className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-base font-semibold leading-7 text-primary">Testimonials</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Trusted by thousands of businesses
          </p>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Don't just take our word for it. Here's what our customers have to say about SaaSify.
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 grid-rows-1 gap-8 text-sm leading-6 text-muted-foreground sm:mt-20 sm:grid-cols-2 xl:mx-0 xl:max-w-none xl:grid-cols-3">
          {testimonials.map((testimonial, testimonialIdx) => (
            <div
              key={testimonialIdx}
              className="space-y-8 rounded-2xl p-6 shadow-sm ring-1 ring-muted/10 sm:p-8"
            >
              <p className="text-muted-foreground">"{testimonial.body}"</p>
              <div className="flex items-center gap-x-4">
                <Avatar>
                  {testimonial.author.image ? (
                    <AvatarImage src={testimonial.author.image} alt="" />
                  ) : (
                    <AvatarFallback className="bg-primary/10 text-primary">
                      {testimonial.author.initials}
                    </AvatarFallback>
                  )}
                </Avatar>
                <div>
                  <div className="font-semibold text-foreground">{testimonial.author.name}</div>
                  <div className="text-xs text-muted-foreground">{testimonial.author.role}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}