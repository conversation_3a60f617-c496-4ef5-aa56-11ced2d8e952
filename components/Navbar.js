'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Menu, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
} from '@/components/ui/dropdown-menu'

export default function Navbar() {
    const [isOpen, setIsOpen] = useState(false)

    return (
        <nav className="bg-white border-b border-gray-200 shadow-sm fixed w-full z-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between h-16 items-center">
                    {/* Logo */}
                    <Link href="/" className="text-xl font-bold text-gray-800">
                        MySite
                    </Link>

                    {/* Desktop menu */}
                    <div className="hidden md:flex gap-6 items-center">
                        <Link href="/about" className="text-gray-600 hover:text-black">
                            About
                        </Link>
                        <Link href="/services" className="text-gray-600 hover:text-black">
                            Services
                        </Link>
                        <Link href="/contact" className="text-gray-600 hover:text-black">
                            Contact
                        </Link>

                        {/* Dropdown */}
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline">More</Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                                <DropdownMenuItem>
                                    <Link href="/settings">Settings</Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                    <Link href="/profile">Profile</Link>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>

                        {/* Auth Buttons */}
                        <Link href="/login">
                            <Button variant="ghost">Log In</Button>
                        </Link>
                        <Link href="/sign-up">
                            <Button>Sign Up</Button>
                        </Link>
                    </div>

                    {/* Mobile menu button */}
                    <div className="md:hidden">
                        <button
                            onClick={() => setIsOpen(!isOpen)}
                            className="text-gray-600 hover:text-black focus:outline-none"
                        >
                            {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                        </button>
                    </div>
                </div>
            </div>

            {/* Mobile menu */}
            {isOpen && (
                <div className="md:hidden px-4 pb-4 pt-2 bg-white border-t border-gray-200 space-y-2">
                    <Link href="/about" className="block text-gray-600 hover:text-black">
                        About
                    </Link>
                    <Link href="/services" className="block text-gray-600 hover:text-black">
                        Services
                    </Link>
                    <Link href="/contact" className="block text-gray-600 hover:text-black">
                        Contact
                    </Link>
                    <Link href="/settings" className="block text-gray-600 hover:text-black">
                        Settings
                    </Link>
                    <Link href="/profile" className="block text-gray-600 hover:text-black">
                        Profile
                    </Link>
                    <Link href="/sign-in" className="block text-gray-600 hover:text-black">
                        Sign In
                    </Link>
                    <Link href="/sign-up" className="block text-gray-600 hover:text-black">
                        Sign Up
                    </Link>
                </div>
            )}
        </nav>
    )
}
