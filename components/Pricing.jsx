import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Check } from 'lucide-react';

const tiers = [
  {
    name: 'Starter',
    id: 'tier-starter',
    price: { monthly: '$15', annually: '$144' },
    description: 'Perfect for small teams just getting started.',
    features: [
      'Up to 5 team members',
      '10GB storage',
      'Basic analytics',
      'Email support',
      '2 projects',
    ],
    cta: 'Start with Starter',
    mostPopular: false,
  },
  {
    name: 'Professional',
    id: 'tier-professional',
    price: { monthly: '$30', annually: '$288' },
    description: 'Ideal for growing teams that need more.',
    features: [
      'Up to 20 team members',
      '50GB storage',
      'Advanced analytics',
      'Priority email support',
      'Unlimited projects',
      'Custom integrations',
    ],
    cta: 'Start with Pro',
    mostPopular: true,
  },
  {
    name: 'Enterprise',
    id: 'tier-enterprise',
    price: { monthly: '$60', annually: '$576' },
    description: 'For large organizations with advanced needs.',
    features: [
      'Unlimited team members',
      '500GB storage',
      'Premium analytics',
      '24/7 phone & email support',
      'Unlimited projects',
      'Custom integrations',
      'Dedicated account manager',
      'SSO & advanced security',
    ],
    cta: 'Contact Sales',
    mostPopular: false,
  },
];

export function Pricing() {
  return (
    <div className="py-24 sm:py-32" id="pricing">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl sm:text-center">
          <h2 className="text-base font-semibold leading-7 text-primary">Pricing</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">Choose the right plan for you</p>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Start with our free trial. No credit card necessary. Upgrade or downgrade anytime.
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-lg grid-cols-1 items-center gap-y-6 sm:mt-20 sm:gap-y-0 lg:max-w-4xl lg:grid-cols-3">
          {tiers.map((tier, tierIdx) => (
            <Card 
              key={tier.id}
              className={`${tier.mostPopular ? 'relative shadow-2xl scale-105 z-10 border-primary' : 'border-border bg-card/50'} flex flex-col justify-between`}
            >
              {tier.mostPopular ? (
                <div className="absolute -top-5 left-0 right-0 mx-auto w-32 rounded-full bg-primary px-3 py-1 text-center text-xs font-medium text-primary-foreground">
                  Most popular
                </div>
              ) : null}
              <CardHeader>
                <CardTitle>{tier.name}</CardTitle>
                <CardDescription>{tier.description}</CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold tracking-tight text-foreground">{tier.price.monthly}</span>
                  <span className="text-sm font-semibold leading-6 text-muted-foreground">/month</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 text-sm">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex items-center gap-x-2">
                      <Check className="h-4 w-4 flex-none text-primary" />
                      <span className="text-muted-foreground">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full" 
                  variant={tier.mostPopular ? 'default' : 'outline'}
                >
                  {tier.cta}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}