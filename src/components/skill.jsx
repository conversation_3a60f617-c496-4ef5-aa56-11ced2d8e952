import hotwire from "../assets/hotwire.jpg";
export default function Skill() {
  return (
    <section id="skills" className="container mx-auto py-12">
      <h2 className="text-3xl font-bold mb-8 text-center">Skills</h2>
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/ruby/ruby-original.svg"
            className="w-10 h-10 mb-2"
            alt="Ruby"
          />
          <h3 className="text-1xl font-semibold">Ruby</h3>
        </div>
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/rails/rails-plain-wordmark.svg"
            className="w-10 h-10 mb-2"
            alt="Ruby on Rails"
          />
          <h3 className="text-1xl font-semibold">Ruby on Rails</h3>
        </div>
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img src={hotwire} className="w-10 h-10 mb-2" alt="Hotwire" />
          <h3 className="text-1xl font-semibold text-center">Hotwire</h3>
        </div>
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/postgresql/postgresql-original.svg"
            className="w-10 h-10 mb-2"
            alt="PostgreSQL"
          />
          <h3 className="text-1xl font-semibold text-center">PostgreSQL</h3>
        </div>
        {/* <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/sqlite/sqlite-original.svg"
            className="w-10 h-10 mb-2"
            alt="SQLite"
          />
          <h3 className="text-1xl font-semibold text-center">SQLite</h3>
        </div> */}
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/mongodb/mongodb-original.svg"
            className="w-10 h-10 mb-2"
            alt="MongoDB"
          />
          <h3 className="text-1xl font-semibold text-center">MongoDB</h3>
        </div>
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/docker/docker-original.svg"
            className="w-10 h-10 mb-2"
            alt="Docker"
          />
          <h3 className="text-1xl font-semibold text-center">Docker</h3>
        </div>

        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/mysql/mysql-original.svg"
            className="w-10 h-10 mb-2"
            alt="MySQL"
          />
          <h3 className="text-1xl font-semibold text-center">MySQL</h3>
        </div>

        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/html5/html5-original.svg"
            className="w-10 h-10 mb-2"
            alt="HTML"
          />
          <h3 className="text-1xl font-semibold text-center">HTML</h3>
        </div>

        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/css3/css3-original.svg"
            className="w-10 h-10 mb-2"
            alt="CSS"
          />
          <h3 className="text-1xl font-semibold text-center">CSS</h3>
        </div>
        
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/tailwindcss/tailwindcss-original.svg"
            className="w-10 h-10 mb-2"
            alt="TailwindCSS"
          />
          <h3 className="text-1xl font-semibold text-center">TailwindCSS</h3>
        </div>
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/javascript/javascript-original.svg"
            className="w-10 h-10 mb-2"
            alt="JavaScript"
          />
          <h3 className="text-1xl font-semibold text-center">JavaScript</h3>
        </div>
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/azuresqldatabase/azuresqldatabase-original.svg"
            className="w-10 h-10 mb-2"
            alt="SQL"
          />
          <h3 className="text-1xl font-semibold text-center">SQL</h3>
        </div>
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
  <img
    src="https://img.icons8.com/ios-filled/100/api-settings.png"
    className="w-10 h-10 mb-2"
    alt="REST API"
  />
  <h3 className="text-1xl font-semibold text-center">REST API</h3>
</div>
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/git/git-original.svg"
            className="w-10 h-10 mb-2"
            alt="Git/GitHub"
          />
          <h3 className="text-1xl font-semibold text-center">Git/GitHub</h3>
        </div>
        <div className="bg-gray-100 p-3 rounded-lg shadow-md flex flex-col items-center transition-transform duration-500 hover:scale-105">
          <img
            src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/express/express-original.svg"
            className="w-10 h-10 mb-2"
            alt="ExpressJS"
          />
          <h3 className="text-1xl font-semibold text-center">ExpressJS</h3>
        </div>
      </div>
    </section>
  );
}
