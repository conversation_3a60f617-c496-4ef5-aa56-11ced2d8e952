import React, { useState } from 'react';

export default function Header() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    return (
        <header className="fixed top-0 left-0 right-0 z-10 bg-gray-800 py-4">
            <nav className="container mx-auto px-4">
                {/* Mobile Hamburger Button */}
                <div className="flex justify-end md:hidden">
                    <button
                        onClick={toggleMenu}
                        className="text-white focus:outline-none"
                        aria-label="Toggle menu"
                    >
                        <svg
                            className="w-6 h-6"
                            fill="none"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            {isMenuOpen ? (
                                // X icon when menu is open
                                <path d="M6 18L18 6M6 6l12 12" />
                            ) : (
                                // Hamburger icon when menu is closed
                                <path d="M4 6h16M4 12h16M4 18h16" />
                            )}
                        </svg>
                    </button>
                </div>

                {/* Desktop Menu */}
                <ul className="hidden md:flex justify-center space-x-8 text-white">
                    <li>
                        <a href="#about" className="hover:text-gray-400 transition-colors">
                            About
                        </a>
                    </li>
                    <li>
                        <a href="#skills" className="hover:text-gray-400 transition-colors">
                            Skills
                        </a>
                    </li>
                    <li>
                        <a href="#experience" className="hover:text-gray-400 transition-colors">
                            Experience
                        </a>
                    </li>
                    <li>
                        <a href="#projects" className="hover:text-gray-400 transition-colors">
                            Projects
                        </a>
                    </li>
                    <li>
                        <a href="#contact" className="hover:text-gray-400 transition-colors">
                            Contact
                        </a>
                    </li>
                </ul>

                {/* Mobile Menu */}
                <div className={`md:hidden ${isMenuOpen ? 'block' : 'hidden'}`}>
                    <ul className="flex flex-col items-center space-y-4 pt-4 pb-2 text-white">
                        <li>
                            <a 
                                href="#about" 
                                className="hover:text-gray-400 transition-colors block py-2"
                                onClick={() => setIsMenuOpen(false)}
                            >
                                About
                            </a>
                        </li>
                        <li>
                            <a 
                                href="#skills" 
                                className="hover:text-gray-400 transition-colors block py-2"
                                onClick={() => setIsMenuOpen(false)}
                            >
                                Skills
                            </a>
                        </li>
                        <li>
                            <a 
                                href="#experience" 
                                className="hover:text-gray-400 transition-colors block py-2"
                                onClick={() => setIsMenuOpen(false)}
                            >
                                Experience
                            </a>
                        </li>
                        <li>
                            <a 
                                href="#projects" 
                                className="hover:text-gray-400 transition-colors block py-2"
                                onClick={() => setIsMenuOpen(false)}
                            >
                                Projects
                            </a>
                        </li>
                        <li>
                            <a 
                                href="#contact" 
                                className="hover:text-gray-400 transition-colors block py-2"
                                onClick={() => setIsMenuOpen(false)}
                            >
                                Contact
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </header>
    );
}