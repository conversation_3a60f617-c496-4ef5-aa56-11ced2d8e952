import diptoImage from "../assets/dipto.jpg";
export default function About() {
  return (
    <section id="about" className="container mx-auto py-12">
      <div className="flex flex-col md:flex-row items-center justify-center">
        <img
          src={diptoImage}
          alt="Your Name"
          className="w-48 h-48 rounded-full mb-4 md:mr-8"
        />
        <div>
          <h2 className="text-3xl font-bold mb-2">
            Hi, I am Dipto. Nice to meet you.
          </h2>
          <p className="text-lg mb-2">Ruby On Rails Developer</p>
          <p className="text-lg">I am from Dhaka, Bangladesh.</p>
        </div>
      </div>
      <div className="mt-8">
        <h3 className="text-2xl font-bold mb-4">About Me</h3>
        <p className="mb-4 text-xl">
        Ruby on Rails developer skilled in building full-stack web applications.
        Backend development is my area of interest and passion.
        I am committed to delivering high-quality results and seeking
         opportunities to leverage my skills to contribute to impactful
         projects and advance my career.
        </p>
        {/* <a
          href="https://www.dropbox.com/scl/fi/6snd8vdo0e7otmagcn0tx/Dipto_CV_2.pdf?rlkey=8p8jp85giiltiy5g90uzidgid&st=c1bh8zu5&dl=0&dl=1"
          download
          className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Download CV
        </a> */}
          <div className="relative inline-block group">
              <div>
                  <button
                      type="button"
                      className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                      View
                      <svg
                          className="w-4 h-4 ml-2"
                          aria-hidden="true"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                      >
                          <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                          ></path>
                      </svg>
                  </button>
              </div>
              <div
                  className="absolute z-10 bg-white rounded-lg shadow w-44 dark:bg-gray-700 transition-all duration-300 scale-0 group-hover:scale-100 origin-top-right"
              >
                  <div className="py-2 text-sm text-gray-700 dark:text-gray-200">
                      <button
                          onClick={() => window.open('https://drive.google.com/file/d/1pxKe2-jlhCORcBYGq2CgZDjjA9dDsVqQ/view?usp=sharing', '_blank')}
                          className="block w-full text-center px-4 py-2 mb-2 bg-green-500 text-white rounded-lg hover:bg-green-400 dark:bg-green-700 dark:hover:bg-green-600"
                      >
                          Resume
                      </button>
                      <button
                          onClick={() => window.open('https://drive.google.com/file/d/1fImz3gr9Hmf2ITEltZuReG3xEb7qtgE_/view?usp=sharing', '_blank')}
                          className="block w-full text-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 dark:bg-blue-700 dark:hover:bg-blue-600"
                      >
                          CV
                      </button>
                  </div>
              </div>


          </div>
      </div>
    </section>
  );
}
