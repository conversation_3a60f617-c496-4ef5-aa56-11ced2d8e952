import galaxy_tech from "../assets/project_screenshot/galaxy-tech.jpg";
import book_face from "../assets/project_screenshot/book-face.jpg";
import private_events from "../assets/project_screenshot/private-events.jpg";
import members_only from "../assets/project_screenshot/members-only.jpg";
export default function Project() {
  return (
    <section id="projects" className="container mx-auto py-12">
      <h2 className="text-3xl text-center font-bold mb-4">Projects</h2>
      <p className="mb-4 text-lg text-center text-red-400 p-2 rounded">
        All the projects are deployed on Render, so it takes 2 minutes to load.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-gray-200 p-4 rounded flex flex-col">
          <h3 className="text-xl font-bold mb-2">Galaxy Tech</h3>
          <p className="mb-2 flex-grow">
            An e-commerce platform dedicated to selling PC parts, components and
            phones. It offers a wide range of products from CPUs and GPUs to RAM
            and SSDs. Whether you are building a PC from scratch, upgrading a
            current setup, or just looking for specific components, Galaxy Tech
            has you covered. The platform features detailed product descriptions
            and a search engine to help you find exactly what you need.
          </p>
        
          <div className="flex space-x-4 mt-auto">
            <a
              href="https://github.com/DiptoSarkar182/galaxy_tech"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 bg-gray-800 text-white rounded hover:bg-gray-700"
            >
              GitHub
            </a>
            <a
              href="https://galaxy-tech.onrender.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-700"
            >
              Live Demo
            </a>
          </div>
        </div>

        <div className="bg-gray-200 p-4 rounded flex flex-col">
          <h3 className="text-xl font-bold mb-2">Book Face</h3>
          <p className="mb-2 flex-grow">
            Book Face is a social media application built with Ruby on Rails. It
            is inspired by popular social media platforms, featuring core
            functionalities such as user authentication, profiles, posts, adding
            friends, real time chatting, liking, and commenting.
          </p>
          
          <div className="flex space-x-4 mt-auto">
            <a
              href="https://github.com/DiptoSarkar182/book_face"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 bg-gray-800 text-white rounded hover:bg-gray-700"
            >
              GitHub
            </a>
            <a
              href="https://book-face.onrender.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-700"
            >
              Live Demo
            </a>
          </div>
        </div>

        <div className="bg-gray-200 p-4 rounded flex flex-col">
          <h3 className="text-xl font-bold mb-2">Private Events</h3>
          <p className="mb-2 flex-grow">
            This is a event management application built with Ruby on Rails.
          </p>
          
          <div className="flex space-x-4 mt-auto">
            <a
              href="https://github.com/DiptoSarkar182/private_events"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 bg-gray-800 text-white rounded hover:bg-gray-700"
            >
              GitHub
            </a>
            <a
              href="https://private-events-khmj.onrender.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-700"
            >
              Live Demo
            </a>
          </div>
        </div>

        <div className="bg-gray-200 p-4 rounded flex flex-col">
          <h3 className="text-xl font-bold mb-2">Members Only!</h3>
          <p className="mb-2 flex-grow">
            Members Only is an exclusive clubhouse where members can write
            anonymous posts. Inside the clubhouse, members can see who the
            author of a post is, but outside, they can only see the story and
            wonder who wrote it.
          </p>
          
          <div className="flex space-x-4 mt-auto">
            <a
              href="https://github.com/DiptoSarkar182/members_only_ROR"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 bg-gray-800 text-white rounded hover:bg-gray-700"
            >
              GitHub
            </a>
            <a
              href="https://members-only-ror.onrender.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-700"
            >
              Live Demo
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
