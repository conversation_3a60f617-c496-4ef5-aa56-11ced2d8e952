import React from 'react';
import { Calendar, MapPin, Briefcase, Link as LinkIcon } from 'lucide-react';

const experiences = [
    {
        title: "Software Engineer, Intern",
        company: "DevSpace",
        companyUrl: "https://devspacebd.com/", // Add company website URL here
        location: "Dhaka, Bangladesh",
        period: "September, 2024 - Present",
        isCurrent: true,
        responsibilities: [
            // "Developing a full-stack restaurant management web application using Ruby on Rails.",
            // "Implementing features for both customer-facing and admin dashboards, including menu management, order tracking, and user authentication.",
            // "Integrating email notifications for improved user experience.",
            // "Collaborating with a team to deliver a scalable, maintainable, and responsive web solution."
        ]
    },
];

export function Experience() {
    return (
        <div className="max-w-4xl mx-auto p-4 sm:p-6 mt-14" id="experience">
            <h2 className="text-2xl sm:text-3xl font-bold mb-8 text-center">Experience</h2>
            
            <div className="relative border-l-2 border-gray-200 pl-4 sm:pl-8 ml-2 sm:ml-4">
                {experiences.map((exp, index) => (
                    <div key={index} className={`mb-10 relative ${!exp.isCurrent ? 'opacity-70' : ''}`}>
                        {/* Timeline dot */}
                        <div className={`absolute -left-[17px] sm:-left-[35px] w-4 h-4 sm:w-6 sm:h-6 ${exp.isCurrent ? 'bg-blue-500' : 'bg-gray-400'} rounded-full border-4 border-white`} />
                        
                        {/* Content */}
                        <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 transition-all hover:shadow-lg">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-2">
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-2 sm:mb-0">{exp.title}</h3>
                                <span className={`font-medium ${exp.isCurrent ? 'text-blue-500' : 'text-gray-500'}`}>
                                    {exp.isCurrent ? 'Present' : 'Past'}
                                </span>
                            </div>
                            
                            <div className="flex flex-col sm:flex-row gap-3 sm:gap-6 text-gray-600 mb-4">
                                <div className="flex items-center gap-2">
                                    <Briefcase className="w-4 h-4 flex-shrink-0" />
                                    <span>{exp.company}</span>
                                    {exp.companyUrl && (
                                        <a 
                                            href={exp.companyUrl}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="inline-flex items-center text-blue-500 hover:text-blue-600 transition-colors"
                                            aria-label={`Visit ${exp.company} website`}
                                        >
                                            <LinkIcon className="w-4 h-4" />
                                        </a>
                                    )}
                                </div>
                                <div className="flex items-center gap-2">
                                    <MapPin className="w-4 h-4 flex-shrink-0" />
                                    <span className="break-words">{exp.location}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Calendar className="w-4 h-4 flex-shrink-0" />
                                    <span>{exp.period}</span>
                                </div>
                            </div>
                            
                            {exp.responsibilities && (
                                <div className="text-gray-600">
                                    <ul className="list-disc ml-4 space-y-2 text-sm sm:text-base">
                                        {exp.responsibilities.map((resp, idx) => (
                                            <li key={idx} className="break-words">{resp}</li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}

export default Experience;